'use server';

import { ClientFilters } from "./client-filters";
import { FilterState, Branch } from "../../types";
import { createClient } from '@/services/supabase/server';
import { getTenantSlug } from "@/services/tenant/tenant-service";

async function getBranches(): Promise<Branch[]> {
  try {
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      console.error("Não foi possível obter o slug do tenant");
      return [];
    }

    // Primeiro obter o tenant_id a partir do slug
    const supabase = await createClient();
    const { data: tenantData, error: tenantError } = await supabase
      .from("tenants")
      .select("id")
      .eq("slug", tenantSlug)
      .single();

    if (tenantError || !tenantData) {
      console.error("Erro ao buscar o tenant:", tenantError);
      return [];
    }

    const tenantId = tenantData.id;

    // Agora buscar as filiais do tenant
    const { data: branchesData, error: branchesError } = await supabase
      .from("branches")
      .select("id, name, address, is_main")
      .eq("tenant_id", tenantId)
      .order("name");

    if (branchesError) {
      console.error("Erro ao buscar filiais:", branchesError);
      return [];
    }

    return branchesData || [];
  } catch (error) {
    console.error("Erro ao buscar filiais:", error);
    return [];
  }
}

interface ServerFiltersProps {
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
}

export async function ServerFilters({ filters, onFilterChange }: ServerFiltersProps) {
  const branches = await getBranches();
  
  return (
    <ClientFilters 
      branches={branches} 
    />
  );
} 